import config from '@skillspace/eslint-service/eslint.config';

export default [
    // ...config,
    // временно только для этого сервиса
    ...config.map((entry) => {
        if (!entry.rules) return entry;
        return {
            ...entry,
            rules: Object.fromEntries(
                Object.entries(entry.rules).map(([ruleName, ruleConfig]) => {
                    if (ruleConfig === 'error') return [ruleName, 'warn'];
                    if (Array.isArray(ruleConfig) && ruleConfig[0] === 'error')
                        return [ruleName, ['warn', ...ruleConfig.slice(1)]];
                    return [ruleName, ruleConfig];
                }),
            ),
        };
    }),

    {
        files: ['**/*.ts'],
        languageOptions: {
            parserOptions: {
                project: './tsconfig.json',
                tsconfigRootDir: import.meta.dirname,
            },
        },
        settings: {
            'import/resolver': {
                typescript: {
                    project: './tsconfig.json',
                },
            },
        },
    },
];
