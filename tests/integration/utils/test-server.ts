import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../../../src/app.module';
import { CollaborationModule } from '../../../src/modules/editor/presentation/server/collaboration.module';
import { HttpAdapterHost } from '@nestjs/core';

export class TestServer {
    private app: INestApplication;
    private httpServer: any;
    private port: number;

    constructor(port: number = 0) {
        this.port = port;
    }

    async start(): Promise<{ app: INestApplication; httpServer: any; port: number }> {
        const moduleFixture: TestingModule = await Test.createTestingModule({
            imports: [AppModule],
        }).compile();

        this.app = moduleFixture.createNestApplication();

        // Enable CORS for testing
        this.app.enableCors({
            origin: true,
            credentials: true,
        });

        await this.app.init();

        // Get the HTTP server
        const httpAdapterHost = this.app.get(HttpAdapterHost);
        this.httpServer = httpAdapterHost.httpAdapter.getHttpServer();

        // Start listening
        await this.app.listen(this.port);

        // Get the actual port if we used 0 (random port)
        const address = this.httpServer.address();
        this.port = address.port;

        return {
            app: this.app,
            httpServer: this.httpServer,
            port: this.port,
        };
    }

    async stop(): Promise<void> {
        if (this.app) {
            await this.app.close();
        }
    }

    getPort(): number {
        return this.port;
    }

    getApp(): INestApplication {
        return this.app;
    }

    getHttpServer(): any {
        return this.httpServer;
    }
}
