import { Field, ID, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';

@ObjectType()
export class PageType {
    @Field(() => ID)
    id: string;

    @Field({ nullable: true })
    title?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    content?: any;

    @Field({ nullable: true })
    textContent?: string;

    @Field()
    isLocked: boolean;

    // Новые обязательные поля
    @Field()
    serviceId: string;

    @Field()
    entityId: string;

    @Field()
    schoolId: string;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;

    @Field({ nullable: true })
    deletedAt?: Date;
}
