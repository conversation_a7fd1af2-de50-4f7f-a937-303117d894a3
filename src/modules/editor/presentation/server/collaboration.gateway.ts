import { Redis } from '@hocuspocus/extension-redis';
import { Hocuspocus, Server as HocuspocusServer } from '@hocuspocus/server';
import { Injectable } from '@nestjs/common';
import { IncomingMessage } from 'http';
import WebSocket from 'ws';

import { appConfig } from '../../../../configs/app.config';
import { createRetryStrategy, parseRedisUrl, RedisConfig } from '../../../shared/utils';
import { LoggerExtension } from './extensions/logger.extension';
import { PersistenceExtension } from './extensions/persistence.extension';

@Injectable()
export class CollaborationGateway {
    private hocuspocus: Hocuspocus;
    private redisConfig: RedisConfig;

    constructor(
        private persistenceExtension: PersistenceExtension,
        private loggerExtension: LoggerExtension,
    ) {
        this.redisConfig = parseRedisUrl(appConfig.redisUrl);

        this.hocuspocus = HocuspocusServer.configure({
            debounce: 10000,
            maxDebounce: 45000,
            unloadImmediately: false,
            extensions: [
                // authenticationExtension,
                this.persistenceExtension,
                this.loggerExtension,
                ...(appConfig.isCollabDisableRedis()
                    ? []
                    : [
                          new Redis({
                              host: this.redisConfig.host,
                              port: this.redisConfig.port,
                              options: {
                                  password: this.redisConfig.password,
                                  db: this.redisConfig.db,
                                  family: this.redisConfig.family,
                                  retryStrategy: createRetryStrategy(),
                              },
                          }),
                      ]),
            ],
        });
    }

    handleConnection(client: WebSocket, request: IncomingMessage): any {
        console.log(`Hocuspocus handleConnection called with URL: ${request.url}`);
        this.hocuspocus.handleConnection(client, request);
    }

    getConnectionCount() {
        return this.hocuspocus.getConnectionsCount();
    }

    getDocumentCount() {
        return this.hocuspocus.getDocumentsCount();
    }
}
