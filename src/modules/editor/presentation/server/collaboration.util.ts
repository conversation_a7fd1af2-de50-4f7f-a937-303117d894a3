import { generateText, getSchema, JSONContent } from '@tiptap/core';
import { Color } from '@tiptap/extension-color';
import { Highlight } from '@tiptap/extension-highlight';
import SubScript from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
// import Table from '@tiptap/extension-table';
// import TableCell from '@tiptap/extension-table-cell';
// import TableHeader from '@tiptap/extension-table-header';
// import TableRow from '@tiptap/extension-table-row';
import { TaskItem } from '@tiptap/extension-task-item';
import { TaskList } from '@tiptap/extension-task-list';
import { TextAlign } from '@tiptap/extension-text-align';
import { TextStyle } from '@tiptap/extension-text-style';
import { Typography } from '@tiptap/extension-typography';
import { Underline } from '@tiptap/extension-underline';
import { Youtube } from '@tiptap/extension-youtube';
import { generateHTML, generateJSON } from '@tiptap/html';
import { Node } from '@tiptap/pm/model';
import { StarterKit } from '@tiptap/starter-kit';

export const tiptapExtensions = [
    StarterKit,
    TextAlign.configure({ types: ['heading', 'paragraph'] }),
    TaskList,
    TaskItem,
    Underline,
    Superscript,
    SubScript,
    Highlight,
    Typography,
    TextStyle,
    Color,
    // Table,
    // TableRow,
    // TableHeader,
    // TableCell,
    Youtube,
] as any;

export function jsonToHtml(tiptapJson: any) {
    return generateHTML(tiptapJson, tiptapExtensions);
}

export function htmlToJson(html: string) {
    return generateJSON(html, tiptapExtensions);
}

export function jsonToText(tiptapJson: JSONContent) {
    return generateText(tiptapJson, tiptapExtensions);
}

export function jsonToNode(tiptapJson: JSONContent) {
    return Node.fromJSON(getSchema(tiptapExtensions), tiptapJson);
}

export function getPageId(documentName: string) {
    if (!documentName) {
        return undefined;
    }

    // Handle format: page.{id}
    if (documentName.startsWith('page.')) {
        return documentName.split('.')[1];
    }

    // For other formats, treat the whole string as the document name
    // This allows testing with simple names like 'example-document'
    return documentName;
}
