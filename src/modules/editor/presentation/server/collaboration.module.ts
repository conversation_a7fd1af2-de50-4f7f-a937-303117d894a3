import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { IncomingMessage } from 'http';
import { WebSocket } from 'ws';

import { EditorModule } from '../../editor.module';
import { CollabWsAdapter } from './adapter/collab-ws.adapter';
import { CollaborationGateway } from './collaboration.gateway';
import { LoggerExtension } from './extensions/logger.extension';
import { PersistenceExtension } from './extensions/persistence.extension';

@Module({
    providers: [CollaborationGateway, PersistenceExtension, LoggerExtension],
    exports: [CollaborationGateway],
    imports: [EditorModule],
})
export class CollaborationModule implements OnModuleInit, OnModuleDestroy {
    private readonly logger = new Logger(CollaborationModule.name);
    private collabWsAdapter: CollabWsAdapter;
    private path = '/collab';

    constructor(
        private readonly collaborationGateway: CollaborationGateway,
        private readonly httpAdapterHost: HttpAdapterHost,
    ) {}

    onModuleInit() {
        this.collabWsAdapter = new CollabWsAdapter();
        const httpServer = this.httpAdapterHost.httpAdapter.getHttpServer();

        const wss = this.collabWsAdapter.handleUpgrade(this.path, httpServer);

        wss.on('connection', (client: WebSocket, request: IncomingMessage) => {
            this.collaborationGateway.handleConnection(client, request);

            client.on('error', (error) => {
                this.logger.error('WebSocket client error:', error);
            });
        });

        wss.on('error', (error) => this.logger.log('WebSocket server error:', error));
    }

    async onModuleDestroy(): Promise<void> {
        if (this.collabWsAdapter) {
            this.collabWsAdapter.destroy();
        }
    }
}
