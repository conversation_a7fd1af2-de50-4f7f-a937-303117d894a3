import { Extension, onLoadDocumentPayload, onStoreDocumentPayload } from '@hocuspocus/server';
import { Injectable, Logger } from '@nestjs/common';
import * as Y from 'yjs';

import { Page } from '../../../../../database/schema';
import { PageService } from '../../../application/services/page.service';
import { getPageId } from '../collaboration.util';

@Injectable()
export class PersistenceExtension implements Extension {
    private readonly logger = new Logger(PersistenceExtension.name);

    constructor(private readonly pageService: PageService) {}

    public async onLoadDocument(data: onLoadDocumentPayload) {
        const { documentName, document } = data;

        if (!document.isEmpty('default')) {
            return;
        }

        if (!documentName) {
            return new Y.Doc();
        }

        // получаем документ
        const pageId = getPageId(documentName);
        const page = await this.pageService.findByIdOrNull(pageId);

        if (page?.ydoc) {
            // Восстановление Y.Doc из бинарных данных
            const ydoc = new Y.Doc();
            Y.applyUpdate(ydoc, page.ydoc);
            return ydoc;
        }

        // TODO: в будущем не создаем документ через ws
        this.logger.debug(`Создание нового Y.Doc для: ${pageId} (страница будет создана при первом сохранении)`);
        return new Y.Doc();
    }

    public async onStoreDocument(data: onStoreDocumentPayload) {
        const { documentName, document } = data;

        if (!documentName) {
            return;
        }

        const pageId = getPageId(documentName);
        let page = await this.pageService.findByIdOrNull(pageId); // TODO: потом бросать ошибку

        // TODO: в будущем не создаем страницу через ws (на момент сохранения она должна быть создана)
        if (!page) {
            page = await this.pageService.create(
                {
                    serviceId: 'webinars',
                    entityId: 'streamId',
                    schoolId: 'schoolUuid',
                },
                pageId,
            );
        }

        // Конвертируем Y.Doc в бинарный формат
        const binaryUpdate = Y.encodeStateAsUpdate(document);
        const binaryBuffer = Buffer.from(binaryUpdate);

        // Проверяем изменения и обновляем при необходимости
        if (this.hasDataChanged(page, binaryBuffer, pageId)) {
            await this.pageService.update(pageId, { ydoc: binaryBuffer });
        }
    }

    // async onChange(data: onChangePayload) {
    //     void data;
    // }

    // async afterUnloadDocument(data: afterUnloadDocumentPayload): Promise<void> {
    //     void data;
    // }

    /**
     * Проверка изменений в бинарных данных
     */
    private hasDataChanged(existingPage: Page, newBinaryData: Buffer, pageId: string): boolean {
        if (existingPage.ydoc && Buffer.compare(existingPage.ydoc, newBinaryData) === 0) {
            this.logger.debug(`Изменения не обнаружены для документа: ${pageId}`);
            return false;
        }
        return true;
    }
}
